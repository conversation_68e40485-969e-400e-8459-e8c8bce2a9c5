import { initializeApp, getApps } from 'firebase/app';
import { 
  getAuth, 
  signInWithEmailAndPassword, 
  signOut,
  onAuthStateChanged 
} from 'firebase/auth';
import { 
  getFirestore, 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  addDoc,   
  serverTimestamp 
} from 'firebase/firestore';
import dotenv from 'dotenv';
dotenv.config(); // This will load .env, .env.local, etc. from the current working directory

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || `${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseapp.com`,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || `${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.appspot.com`,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "",
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Initialize Firebase if apps haven't been initialized
if (!getApps().length) {
  initializeApp(firebaseConfig);
}

// Auth instance
const auth = getAuth();
// Firestore instance
const db = getFirestore();

// Authentication functions
export const loginWithEmailAndPassword = async (email, password) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return { success: true, user: userCredential.user };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const logoutUser = async () => {
  try {
    await signOut(auth);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const getCurrentUser = () => {
  return new Promise((resolve, reject) => {
    const unsubscribe = onAuthStateChanged(
      auth,
      (user) => {
        unsubscribe();
        resolve(user);
      },
      reject
    );
  });
};

// Firestore staff functions
export const getStaffByUserId = async (userId) => {
  try {
    const staffRef = doc(db, 'staff', userId);
    const staffDoc = await getDoc(staffRef);
    
    if (staffDoc.exists()) {
      return { success: true, data: { id: staffDoc.id, ...staffDoc.data() } };
    } else {
      return { success: false, error: 'Staff record not found' };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const getStaffByEmail = async (email) => {
  try {
    const staffQuery = query(
      collection(db, 'staff'),
      where('email', '==', email)
    );
    
    const querySnapshot = await getDocs(staffQuery);
    
    if (querySnapshot.empty) {
      return { success: false, error: 'Staff record not found' };
    }
    
    // Get the first matching document
    const staffDoc = querySnapshot.docs[0];
    return { success: true, data: { id: staffDoc.id, ...staffDoc.data() } };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const getStaffByHospitalId = async (hospitalId) => {
  try {
    const staffQuery = query(
      collection(db, 'staff'),
      where('hospital_id', '==', hospitalId)
    );
    
    const querySnapshot = await getDocs(staffQuery);
    const staffList = [];
    
    querySnapshot.forEach((doc) => {
      staffList.push({ id: doc.id, ...doc.data() });
    });
    
    return { success: true, data: staffList };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const createStaffMember = async (staffData) => {
  try {
    const newStaffRef = doc(collection(db, 'staff'));
    await setDoc(newStaffRef, {
      ...staffData,
      created_at: serverTimestamp(),
      updated_at: serverTimestamp()
    });
    
    return { success: true, id: newStaffRef.id };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const updateStaffMember = async (staffId, staffData) => {
  try {
    const staffRef = doc(db, 'staff', staffId);
    await updateDoc(staffRef, {
      ...staffData,
      updated_at: serverTimestamp()
    });
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const deleteStaffMember = async (staffId) => {
  try {
    await deleteDoc(doc(db, 'staff', staffId));
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

// Doctor management
export const getDoctorsByHospitalId = async (hospitalId) => {
  try {
    // Validate hospital ID format using helper function
    const validation = validateHospitalId(hospitalId);
    if (!validation.isValid) {
      console.error(`[FIREBASE_LIB] getDoctorsByHospitalId: ${validation.error}`);
      return { success: false, error: validation.error };
    }

    const doctorsCollectionPath = `hospital_${hospitalId}_data/doctors/doctors`;
    console.log(`[FIREBASE_LIB] Querying doctors from path: ${doctorsCollectionPath}`);
    
    const doctorsRef = collection(db, doctorsCollectionPath);
    const querySnapshot = await getDocs(doctorsRef);
    const doctorsList = [];

    querySnapshot.forEach((doc) => {
      doctorsList.push({ id: doc.id, ...doc.data() });
    });

    if (doctorsList.length === 0) {
      console.warn(`[FIREBASE_LIB] No doctors found at path: ${doctorsCollectionPath} for hospitalId: ${hospitalId}`);
    }

    return { success: true, data: doctorsList };
  } catch (error) {
    console.error(`[FIREBASE_LIB] Error fetching doctors from ${`hospital_${hospitalId}_data/doctors/doctors`} for hospitalId ${hospitalId}:`, error);
    return { success: false, error: error.message };
  }
};

export const getDoctor = async (hospitalId, doctorId) => {
  try {
    const hospitalDataDocId = `hospital_${hospitalId}_data`;
    const doctorRef = doc(db, hospitalDataDocId, 'doctors', 'doctors', doctorId);
    
    const doctorDoc = await getDoc(doctorRef);
    
    if (doctorDoc.exists()) {
      return { success: true, data: { id: doctorDoc.id, ...doctorDoc.data() } };
    } else {
      return { success: false, error: 'Doctor not found' };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const createDoctor = async (doctorData) => {
  try {
    const newDoctorRef = doc(collection(db, 'doctors'));
    await setDoc(newDoctorRef, {
      ...doctorData,
      created_at: serverTimestamp(),
      updated_at: serverTimestamp()
    });
    
    return { success: true, id: newDoctorRef.id };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const updateDoctor = async (doctorId, doctorData) => {
  try {
    const doctorRef = doc(db, 'doctors', doctorId);
    await updateDoc(doctorRef, {
      ...doctorData,
      updated_at: serverTimestamp()
    });
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const deleteDoctor = async (doctorId) => {
  try {
    await deleteDoc(doc(db, 'doctors', doctorId));
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const getDoctorById = async (doctorId) => {
  try {
    const doctorRef = doc(db, 'doctors', doctorId);
    const doctorDoc = await getDoc(doctorRef);

    if (doctorDoc.exists()) {
      return { success: true, data: { id: doctorDoc.id, ...doctorDoc.data() } };
    } else {
      return { success: false, error: 'Doctor not found' };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
};

// Hospital settings
export const getHospitalSettings = async (hospitalId) => {
  try {
    const hospitalRef = doc(db, 'hospitals', hospitalId);
    const hospitalDoc = await getDoc(hospitalRef);
    
    if (hospitalDoc.exists()) {
      return { success: true, data: { id: hospitalDoc.id, ...hospitalDoc.data() } };
    } else {
      return { success: false, error: 'Hospital settings not found' };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const updateHospitalSettings = async (hospitalId, settingsData) => {
  try {
    const hospitalRef = doc(db, 'hospitals', hospitalId);
    const hospitalDoc = await getDoc(hospitalRef);
    
    if (hospitalDoc.exists()) {
      await updateDoc(hospitalRef, {
        ...settingsData,
        updated_at: serverTimestamp()
      });
    } else {
      await setDoc(hospitalRef, {
        ...settingsData,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp()
      });
    }
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

// Search functionality
export const searchFirestore = async (hospitalId, searchTerm) => {
  try {
    // Since Firestore doesn't support text search natively, we'll do client-side filtering
    // Get all doctors and staff for this hospital
    const [doctorsResult, staffResult] = await Promise.all([
      getDoctorsByHospitalId(hospitalId),
      getStaffByHospitalId(hospitalId)
    ]);
    
    let results = {
      doctors: [],
      staff: []
    };
    
    if (doctorsResult.success) {
      // Filter doctors based on search term
      results.doctors = doctorsResult.data.filter(doctor => 
        doctor.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doctor.specialty?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doctor.email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    if (staffResult.success) {
      // Filter staff based on search term
      results.staff = staffResult.data.filter(staff => 
        staff.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        staff.role?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        staff.email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    return { success: true, data: results };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

// Booking limit management functions
export const updateDoctorBookingLimits = async (hospitalId, doctorId, dailyLimits) => {
  try {
    // Validate hospital ID format using helper function
    const validation = validateHospitalId(hospitalId);
    if (!validation.isValid) {
      return { success: false, error: validation.error };
    }

    if (!doctorId || !dailyLimits) {
      return { success: false, error: 'Missing required parameters: doctorId and dailyLimits are required' };
    }

    // Validate daily limits
    const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    for (const day of validDays) {
      if (dailyLimits[day] === undefined || dailyLimits[day] === null) {
        return { success: false, error: `Missing limit for ${day}` };
      }

      const limit = parseInt(dailyLimits[day]);
      if (isNaN(limit) || limit < 0 || limit > 50) {
        return { success: false, error: `Invalid limit for ${day}: must be between 0 and 50` };
      }

      // Ensure the value is stored as integer
      dailyLimits[day] = limit;
    }

    const doctorRef = doc(db, `hospital_${hospitalId}_data`, 'doctors', 'doctors', doctorId);

    // Update the doctor document with new booking limits
    await updateDoc(doctorRef, {
      daily_booking_limits: dailyLimits,
      updated_at: serverTimestamp()
    });

    return { success: true };
  } catch (error) {
    console.error('[FIREBASE_LIB] Error updating doctor booking limits:', error);
    return { success: false, error: error.message };
  }
};

export const getDoctorBookingLimits = async (hospitalId, doctorId) => {
  try {
    if (!hospitalId || !doctorId) {
      return { success: false, error: 'Missing required parameters' };
    }

    const doctorRef = doc(db, `hospital_${hospitalId}_data`, 'doctors', 'doctors', doctorId);
    const doctorDoc = await getDoc(doctorRef);

    if (doctorDoc.exists()) {
      const doctorData = doctorDoc.data();
      const dailyLimits = doctorData.daily_booking_limits || {};

      // Ensure all days have default values
      const defaultLimits = {
        monday: 10,
        tuesday: 10,
        wednesday: 10,
        thursday: 10,
        friday: 10,
        saturday: 0,
        sunday: 0
      };

      const completeLimits = { ...defaultLimits, ...dailyLimits };

      return {
        success: true,
        data: {
          doctorId,
          doctorName: doctorData.name || 'Unknown Doctor',
          specialty: doctorData.specialty || 'General Medicine',
          dailyLimits: completeLimits
        }
      };
    } else {
      return { success: false, error: 'Doctor not found' };
    }
  } catch (error) {
    console.error('[FIREBASE_LIB] Error getting doctor booking limits:', error);
    return { success: false, error: error.message };
  }
};

export const getAllDoctorsBookingLimits = async (hospitalId) => {
  try {
    if (!hospitalId) {
      return { success: false, error: 'Hospital ID is required' };
    }

    const doctorsCollectionPath = `hospital_${hospitalId}_data/doctors/doctors`;
    const doctorsRef = collection(db, doctorsCollectionPath);
    const querySnapshot = await getDocs(doctorsRef);

    const doctorsWithLimits = [];

    querySnapshot.forEach((doc) => {
      const doctorData = doc.data();
      const dailyLimits = doctorData.daily_booking_limits || {};

      // Ensure all days have default values
      const defaultLimits = {
        monday: 10,
        tuesday: 10,
        wednesday: 10,
        thursday: 10,
        friday: 10,
        saturday: 0,
        sunday: 0
      };

      const completeLimits = { ...defaultLimits, ...dailyLimits };

      doctorsWithLimits.push({
        id: doc.id,
        name: doctorData.name || 'Unknown Doctor',
        specialty: doctorData.specialty || 'General Medicine',
        dailyLimits: completeLimits,
        email: doctorData.email || '',
        phone: doctorData.phone || ''
      });
    });

    return { success: true, data: doctorsWithLimits };
  } catch (error) {
    console.error('[FIREBASE_LIB] Error getting all doctors booking limits:', error);
    return { success: false, error: error.message };
  }
};

// Availability management functions
export const getDoctorsAndTestsByDate = async (hospitalId, date) => {
  try {
    if (!hospitalId || !date) {
      return { success: false, error: 'Hospital ID and date are required' };
    }

    // Get doctors
    const doctorsResult = await getDoctorsByHospitalId(hospitalId);
    let doctors = [];
    if (doctorsResult.success) {
      doctors = doctorsResult.data.map(doctor => ({
        id: doctor.id,
        name: doctor.name,
        specialty: doctor.specialty || 'General Medicine',
        type: 'doctor',
        available: doctor.daily_availability?.[date] !== false // Default to true if not set
      }));
    }

    // Get tests
    const testsResult = await getTestsByHospitalId(hospitalId);
    let tests = [];
    if (testsResult.success) {
      tests = testsResult.data.map(test => ({
        id: test.id,
        name: test.name,
        description: test.description || '',
        cost: test.cost || 0,
        type: 'test',
        available: test.daily_availability?.[date] !== false // Default to true if not set
      }));
    }

    return {
      success: true,
      data: {
        doctors,
        tests,
        date
      }
    };
  } catch (error) {
    console.error('[FIREBASE_LIB] Error getting doctors and tests by date:', error);
    return { success: false, error: error.message };
  }
};

export const getTestsByHospitalId = async (hospitalId) => {
  try {
    // Validate hospital ID format using helper function
    const validation = validateHospitalId(hospitalId);
    if (!validation.isValid) {
      console.error(`[FIREBASE_LIB] getTestsByHospitalId: ${validation.error}`);
      return { success: false, error: validation.error };
    }

    const testsCollectionPath = `hospital_${hospitalId}_data/test_info/tests`;
    console.log(`[FIREBASE_LIB] Querying tests from path: ${testsCollectionPath}`);

    const testsRef = collection(db, testsCollectionPath);
    const querySnapshot = await getDocs(testsRef);
    const testsList = [];

    querySnapshot.forEach((doc) => {
      testsList.push({ id: doc.id, ...doc.data() });
    });

    if (testsList.length === 0) {
      console.warn(`[FIREBASE_LIB] No tests found at path: ${testsCollectionPath} for hospitalId: ${hospitalId}`);
    }

    return { success: true, data: testsList };
  } catch (error) {
    console.error(`[FIREBASE_LIB] Error fetching tests from ${`hospital_${hospitalId}_data/test_info/tests`} for hospitalId ${hospitalId}:`, error);
    return { success: false, error: error.message };
  }
};

export const updateDoctorAvailability = async (hospitalId, doctorId, date, isAvailable) => {
  try {
    if (!hospitalId || !doctorId || !date || typeof isAvailable !== 'boolean') {
      return { success: false, error: 'Missing required parameters' };
    }

    const doctorRef = doc(db, `hospital_${hospitalId}_data`, 'doctors', 'doctors', doctorId);

    // Get current doctor data
    const doctorDoc = await getDoc(doctorRef);
    if (!doctorDoc.exists()) {
      return { success: false, error: 'Doctor not found' };
    }

    const doctorData = doctorDoc.data();
    const currentAvailability = doctorData.daily_availability || {};

    // Update availability for the specific date
    const updatedAvailability = {
      ...currentAvailability,
      [date]: isAvailable
    };

    await updateDoc(doctorRef, {
      daily_availability: updatedAvailability,
      updated_at: serverTimestamp()
    });

    return { success: true };
  } catch (error) {
    console.error('[FIREBASE_LIB] Error updating doctor availability:', error);
    return { success: false, error: error.message };
  }
};

export const updateTestAvailability = async (hospitalId, testId, date, isAvailable) => {
  try {
    if (!hospitalId || !testId || !date || typeof isAvailable !== 'boolean') {
      return { success: false, error: 'Missing required parameters' };
    }

    const testRef = doc(db, `hospital_${hospitalId}_data`, 'test_info', 'tests', testId);

    // Get current test data
    const testDoc = await getDoc(testRef);
    if (!testDoc.exists()) {
      return { success: false, error: 'Test not found' };
    }

    const testData = testDoc.data();
    const currentAvailability = testData.daily_availability || {};

    // Update availability for the specific date
    const updatedAvailability = {
      ...currentAvailability,
      [date]: isAvailable
    };

    await updateDoc(testRef, {
      daily_availability: updatedAvailability,
      updated_at: serverTimestamp()
    });

    return { success: true };
  } catch (error) {
    console.error('[FIREBASE_LIB] Error updating test availability:', error);
    return { success: false, error: error.message };
  }
};

export const updateMultipleAvailability = async (hospitalId, updates, date) => {
  try {
    if (!hospitalId || !updates || !Array.isArray(updates) || !date) {
      return { success: false, error: 'Missing required parameters' };
    }

    const results = [];

    for (const update of updates) {
      const { id, type, available } = update;

      if (!id || !type || typeof available !== 'boolean') {
        results.push({ id, success: false, error: 'Invalid update data' });
        continue;
      }

      try {
        let result;
        if (type === 'doctor') {
          result = await updateDoctorAvailability(hospitalId, id, date, available);
        } else if (type === 'test') {
          result = await updateTestAvailability(hospitalId, id, date, available);
        } else {
          result = { success: false, error: 'Invalid type' };
        }

        results.push({ id, type, ...result });
      } catch (error) {
        results.push({ id, type, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    return {
      success: successCount > 0,
      data: {
        results,
        summary: {
          total: totalCount,
          successful: successCount,
          failed: totalCount - successCount
        }
      }
    };
  } catch (error) {
    console.error('[FIREBASE_LIB] Error updating multiple availability:', error);
    return { success: false, error: error.message };
  }
};

// Hospital ID validation helper function
/**
 * Validates hospital ID format for consistency with voice_agent validation.
 *
 * @param {string} hospitalId - The hospital ID to validate
 * @returns {{isValid: boolean, error?: string}} Validation result
 */
const validateHospitalId = (hospitalId) => {
  if (!hospitalId || typeof hospitalId !== 'string') {
    return { isValid: false, error: 'Hospital ID is required and must be a string' };
  }

  // Validate hospital ID format (alphanumeric with underscores and hyphens, 1-50 chars)
  // This matches the validation in voice_agent/api_endpoints.py
  if (!/^[a-zA-Z0-9_-]+$/.test(hospitalId) || hospitalId.length < 1 || hospitalId.length > 50) {
    return {
      isValid: false,
      error: 'Invalid hospital ID format. Only alphanumeric characters, underscores, and hyphens are allowed (1-50 characters).'
    };
  }

  return { isValid: true };
};

// Hospital management functions

/**
 * Retrieves all hospitals from the Firestore database.
 *
 * @async
 * @returns {Promise<{success: boolean, data?: Array<Object>, error?: string}>}
 *   - success: true if operation succeeded
 *   - data: Array of hospital objects with id, name, languages, services, etc.
 *   - error: Error message if operation failed
 * @example
 * const result = await getAllHospitals();
 * if (result.success) {
 *   console.log('Hospitals:', result.data);
 * }
 */
export const getAllHospitals = async () => {
  try {
    const hospitalsRef = collection(db, 'hospitals');
    const querySnapshot = await getDocs(hospitalsRef);
    const hospitalsList = [];

    querySnapshot.forEach((doc) => {
      const hospitalConfig = doc.data();
      const docId = doc.id;

      // Extract hospital ID from document ID (format: hospital_{id}_data)
      // Use robust regex matching with validation
      const hospitalIdMatch = docId.match(/^hospital_(.+)_data$/);
      if (hospitalIdMatch && hospitalIdMatch[1]) {
        const hospitalId = hospitalIdMatch[1];

        // Validate extracted hospital ID format (consistent with voice_agent validation)
        // Allow alphanumeric characters, underscores, and hyphens (1-50 chars)
        if (!/^[a-zA-Z0-9_-]+$/.test(hospitalId) || hospitalId.length < 1 || hospitalId.length > 50) {
          console.warn(`[FIREBASE_LIB] Invalid hospital ID format extracted: ${hospitalId} from document: ${docId}`);
          return; // Skip this document
        }

        hospitalsList.push({
          id: hospitalId,
          name: hospitalConfig.name || `Hospital ${hospitalId}`,
          languages: hospitalConfig.languages || ['hi', 'bn', 'en'],
          services: hospitalConfig.services || ['doctor_booking', 'test_booking'],
          address: hospitalConfig.address || '',
          phone: hospitalConfig.phone || '',
          email: hospitalConfig.email || '',
          settings: hospitalConfig.settings || {},
          jambonz_application_sid: hospitalConfig.jambonz_application_sid || ''
        });
      }
    });

    return { success: true, data: hospitalsList };
  } catch (error) {
    console.error('[FIREBASE_LIB] Error fetching all hospitals:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Retrieves a specific hospital by ID from the Firestore database.
 *
 * @async
 * @param {string} hospitalId - The unique identifier of the hospital
 * @returns {Promise<{success: boolean, data?: Object, error?: string}>}
 *   - success: true if operation succeeded
 *   - data: Hospital object with id, name, languages, services, etc.
 *   - error: Error message if operation failed or hospital not found
 * @example
 * const result = await getHospitalById('chennai_general');
 * if (result.success) {
 *   console.log('Hospital:', result.data);
 * }
 */
export const getHospitalById = async (hospitalId) => {
  try {
    // Validate hospital ID format using helper function
    const validation = validateHospitalId(hospitalId);
    if (!validation.isValid) {
      return { success: false, error: validation.error };
    }

    const hospitalRef = doc(db, 'hospitals', `hospital_${hospitalId}_data`);
    const hospitalDoc = await getDoc(hospitalRef);

    if (hospitalDoc.exists()) {
      const hospitalConfig = hospitalDoc.data();

      return {
        success: true,
        data: {
          id: hospitalId,
          name: hospitalConfig.name || `Hospital ${hospitalId}`,
          languages: hospitalConfig.languages || ['hi', 'bn', 'en'],
          services: hospitalConfig.services || ['doctor_booking', 'test_booking'],
          address: hospitalConfig.address || '',
          phone: hospitalConfig.phone || '',
          email: hospitalConfig.email || '',
          settings: hospitalConfig.settings || {},
          jambonz_application_sid: hospitalConfig.jambonz_application_sid || ''
        }
      };
    } else {
      return { success: false, error: 'Hospital not found' };
    }
  } catch (error) {
    console.error('[FIREBASE_LIB] Error fetching hospital by ID:', error);
    return { success: false, error: error.message };
  }
};

export { auth, db };