import json
import logging
import inspect
from typing import Dict, Any, Optional, Set, Type, List
from .call_context import Call<PERSON>ontext
from .websocket_manager import WebSocketConnection
from .datetime_utils import get_current_iso_timestamp_fresh

# Module-local logger
logger = logging.getLogger(__name__)

class WebSocketCallContext(CallContext):
    """
    Extended CallContext for WebSocket connections.
    Adds WebSocket-specific metadata and connection tracking.
    """

    # Define WebSocket-specific fields that should not be copied from base context
    _WEBSOCKET_ONLY_FIELDS = {
        'connection_id', 'message_correlation', 'pending_acks',
        'reconnection_count', 'last_message_id', 'websocket_metadata'
    }

    # Define critical fields that must be copied (fallback list)
    _CRITICAL_BASE_FIELDS = {
        'created_at', 'last_updated', 'state', 'language', 'entities',
        'conversation_history', 'state_history', 'data', 'dtmf_history'
    }

    def __init__(self, call_id: Optional[str] = None, hospital_id: Optional[str] = None,
                 caller_number: Optional[str] = None, connection_id: Optional[str] = None):
        """
        Initialize WebSocket call context

        Args:
            call_id: Unique call identifier
            hospital_id: Hospital identifier
            caller_number: Caller's phone number
            connection_id: WebSocket connection identifier
        """
        super().__init__(call_id, hospital_id, caller_number)

        # WebSocket-specific data
        self.connection_id = connection_id
        self.message_correlation: Dict[str, str] = {}  # msgid -> internal_id mapping
        self.pending_acks: Set[str] = set()  # Outstanding message IDs
        self.reconnection_count = 0
        self.last_message_id: Optional[str] = None
        self.websocket_metadata: Dict[str, Any] = {
            "connection_established": get_current_iso_timestamp_fresh(),
            "last_activity": get_current_iso_timestamp_fresh(),
            "protocol_version": "ws.jambonz.org",
            "message_count": 0
        }
        self.jambonz_application_sid = None  # Will be loaded from hospital config

    @classmethod
    def _get_base_context_fields(cls, base_context: CallContext) -> Dict[str, Any]:
        """
        Extract all relevant fields from base CallContext using introspection.
        This approach automatically handles new fields added to CallContext.

        Args:
            base_context: Base CallContext instance

        Returns:
            Dictionary of field names and values to copy
        """
        fields_to_copy = {}

        # Get all attributes from the base context
        for attr_name in dir(base_context):
            # Skip private/protected attributes and methods
            if attr_name.startswith('_'):
                continue

            # Skip methods and properties
            attr_value = getattr(base_context, attr_name)
            if callable(attr_value):
                continue

            # Skip WebSocket-specific fields
            if attr_name in cls._WEBSOCKET_ONLY_FIELDS:
                continue

            # Include the field
            fields_to_copy[attr_name] = attr_value

        return fields_to_copy

    @classmethod
    def _copy_base_context_data(cls, ws_context: 'WebSocketCallContext',
                               base_context: CallContext) -> None:
        """
        Copy all relevant data from base context to WebSocket context.
        Uses introspection to automatically handle all fields.

        Args:
            ws_context: WebSocket context to update
            base_context: Base context to copy from
        """
        try:
            fields_to_copy = cls._get_base_context_fields(base_context)

            for field_name, field_value in fields_to_copy.items():
                # Safely copy the field, handling potential type issues
                try:
                    # For mutable objects, create a copy to avoid shared references
                    if isinstance(field_value, (dict, list, set)):
                        if isinstance(field_value, dict):
                            setattr(ws_context, field_name, field_value.copy())
                        elif isinstance(field_value, list):
                            setattr(ws_context, field_name, field_value.copy())
                        elif isinstance(field_value, set):
                            setattr(ws_context, field_name, field_value.copy())
                    else:
                        # For immutable objects, direct assignment is safe
                        setattr(ws_context, field_name, field_value)

                    logger.debug(f"Copied field '{field_name}' from base context")

                except Exception as e:
                    logger.warning(f"Failed to copy field '{field_name}': {e}")

        except Exception as e:
            logger.error(f"Error copying base context data: {e}")
            # Fall back to manual copying for critical fields
            cls._fallback_copy_critical_fields(ws_context, base_context)

    @classmethod
    def _fallback_copy_critical_fields(cls, ws_context: 'WebSocketCallContext',
                                      base_context: CallContext) -> None:
        """
        Fallback method to copy critical fields manually if introspection fails.

        Args:
            ws_context: WebSocket context to update
            base_context: Base context to copy from
        """
        for field_name in cls._CRITICAL_BASE_FIELDS:
            try:
                if hasattr(base_context, field_name):
                    field_value = getattr(base_context, field_name)
                    # Handle mutable objects properly
                    if isinstance(field_value, (dict, list, set)):
                        if isinstance(field_value, dict):
                            setattr(ws_context, field_name, field_value.copy())
                        elif isinstance(field_value, list):
                            setattr(ws_context, field_name, field_value.copy())
                        elif isinstance(field_value, set):
                            setattr(ws_context, field_name, field_value.copy())
                    else:
                        setattr(ws_context, field_name, field_value)
                    logger.debug(f"Fallback copied critical field '{field_name}'")
            except Exception as e:
                logger.error(f"Failed to copy critical field '{field_name}': {e}")
    
    @staticmethod
    async def get_or_create_websocket(call_id: Optional[str] = None,
                                    hospital_id: Optional[str] = None,
                                    caller_number: Optional[str] = None,
                                    connection_id: Optional[str] = None) -> 'WebSocketCallContext':
        """
        Factory method to get or create WebSocket call context.
        Uses introspection to automatically copy all relevant fields from base context.

        Args:
            call_id: Call identifier
            hospital_id: Hospital identifier
            caller_number: Caller number
            connection_id: WebSocket connection ID

        Returns:
            WebSocketCallContext instance
        """
        # First try to get existing context
        if call_id:
            try:
                # Load base context
                base_ctx = await CallContext.get_or_create(call_id, hospital_id, caller_number)

                # Create WebSocket context with basic identifiers
                ws_ctx = WebSocketCallContext(
                    call_id=base_ctx.call_id,
                    hospital_id=base_ctx.hospital_id,
                    caller_number=base_ctx.caller_number,
                    connection_id=connection_id
                )

                # Copy all base context data using introspection
                WebSocketCallContext._copy_base_context_data(ws_ctx, base_ctx)

                # Load WebSocket-specific data if exists
                await ws_ctx._load_websocket_data()

                logger.info(f"Successfully created WebSocket context from existing base context for call {call_id}")
                return ws_ctx

            except Exception as e:
                logger.error(f"Error loading existing context for call {call_id}: {e}")
                # Continue to create new context below

        # Create new WebSocket context
        logger.info(f"Creating new WebSocket context for call {call_id or 'new'}")
        ws_ctx = WebSocketCallContext(call_id, hospital_id, caller_number, connection_id)

        # Save the new context
        save_success = await ws_ctx.save()
        if not save_success:
            logger.warning(f"Failed to save new WebSocket context for call {ws_ctx.call_id}")

        return ws_ctx

    @classmethod
    def validate_field_copying(cls, ws_context: 'WebSocketCallContext',
                              base_context: CallContext) -> Dict[str, Any]:
        """
        Validate that all expected fields were copied correctly from base context.
        This method is useful for testing and debugging.

        Args:
            ws_context: WebSocket context to validate
            base_context: Original base context

        Returns:
            Dictionary with validation results
        """
        validation_results = {
            "success": True,
            "copied_fields": [],
            "missing_fields": [],
            "mismatched_fields": [],
            "websocket_only_fields": list(cls._WEBSOCKET_ONLY_FIELDS)
        }

        try:
            base_fields = cls._get_base_context_fields(base_context)

            for field_name, expected_value in base_fields.items():
                if hasattr(ws_context, field_name):
                    actual_value = getattr(ws_context, field_name)

                    # For mutable objects, check content equality
                    if isinstance(expected_value, (dict, list, set)):
                        if actual_value == expected_value:
                            validation_results["copied_fields"].append(field_name)
                        else:
                            validation_results["mismatched_fields"].append({
                                "field": field_name,
                                "expected": expected_value,
                                "actual": actual_value
                            })
                            validation_results["success"] = False
                    else:
                        # For immutable objects, direct comparison
                        if actual_value == expected_value:
                            validation_results["copied_fields"].append(field_name)
                        else:
                            validation_results["mismatched_fields"].append({
                                "field": field_name,
                                "expected": expected_value,
                                "actual": actual_value
                            })
                            validation_results["success"] = False
                else:
                    validation_results["missing_fields"].append(field_name)
                    validation_results["success"] = False

        except Exception as e:
            validation_results["success"] = False
            validation_results["error"] = str(e)

        return validation_results

    async def _load_websocket_data(self) -> bool:
        """
        Load WebSocket-specific data from Redis
        
        Returns:
            True if data was loaded, False otherwise
        """
        try:
            from shared.redis.adapters.python_adapter import get_python_adapter
            redis_adapter = get_python_adapter()

            ws_key = f"call:websocket:{self.call_id}"
            ws_data_str = await redis_adapter.get_async(ws_key)
            
            if ws_data_str:
                if isinstance(ws_data_str, bytes):
                    ws_data_str = ws_data_str.decode('utf-8')
                
                ws_data = json.loads(ws_data_str)
                
                self.connection_id = ws_data.get("connection_id", self.connection_id)
                self.message_correlation = ws_data.get("message_correlation", {})
                self.pending_acks = set(ws_data.get("pending_acks", []))
                self.reconnection_count = ws_data.get("reconnection_count", 0)
                self.last_message_id = ws_data.get("last_message_id")
                self.websocket_metadata = ws_data.get("websocket_metadata", self.websocket_metadata)
                
                logger.debug(f"Loaded WebSocket data for call {self.call_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error loading WebSocket data for {self.call_id}: {e}")
        
        return False
    
    async def save_websocket_data(self, ttl: int = 600) -> bool:
        """
        Save WebSocket-specific data to Redis
        
        Args:
            ttl: Time to live in seconds
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            from shared.redis.adapters.python_adapter import get_python_adapter
            adapter = get_python_adapter()
            redis_client = adapter.connection_manager.get_async_client()
            
            ws_data = {
                "connection_id": self.connection_id,
                "message_correlation": self.message_correlation,
                "pending_acks": list(self.pending_acks),
                "reconnection_count": self.reconnection_count,
                "last_message_id": self.last_message_id,
                "websocket_metadata": self.websocket_metadata,
                "last_updated": get_current_iso_timestamp_fresh()
            }
            
            ws_key = f"call:websocket:{self.call_id}"
            await redis_client.setex(ws_key, ttl, json.dumps(ws_data))
            
            logger.debug(f"Saved WebSocket data for call {self.call_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving WebSocket data for {self.call_id}: {e}")
            return False
    
    async def save(self, ttl: int = 600) -> bool:
        """
        Save both base context and WebSocket data
        
        Args:
            ttl: Time to live in seconds
            
        Returns:
            True if both saves successful, False otherwise
        """
        base_saved = await super().save(ttl)
        ws_saved = await self.save_websocket_data(ttl)
        
        return base_saved and ws_saved
    
    async def add_message_correlation(self, message_id: str, internal_id: str) -> bool:
        """
        Add message ID correlation for tracking
        
        Args:
            message_id: Jambonz message ID
            internal_id: Internal tracking ID
            
        Returns:
            True if added successfully
        """
        self.message_correlation[message_id] = internal_id
        self.last_message_id = message_id
        self.websocket_metadata["message_count"] += 1
        self.websocket_metadata["last_activity"] = get_current_iso_timestamp_fresh()
        
        return await self.save_websocket_data()
    
    async def add_pending_ack(self, message_id: str) -> bool:
        """
        Add message ID to pending acknowledgments
        
        Args:
            message_id: Message ID awaiting acknowledgment
            
        Returns:
            True if added successfully
        """
        self.pending_acks.add(message_id)
        return await self.save_websocket_data()
    
    async def remove_pending_ack(self, message_id: str) -> bool:
        """
        Remove message ID from pending acknowledgments
        
        Args:
            message_id: Message ID that was acknowledged
            
        Returns:
            True if removed successfully
        """
        self.pending_acks.discard(message_id)
        return await self.save_websocket_data()
    
    async def handle_reconnection(self, new_connection_id: str) -> bool:
        """
        Handle WebSocket reconnection
        
        Args:
            new_connection_id: New connection identifier
            
        Returns:
            True if handled successfully
        """
        self.connection_id = new_connection_id
        self.reconnection_count += 1
        self.websocket_metadata["last_reconnection"] = get_current_iso_timestamp_fresh()
        self.websocket_metadata["reconnection_count"] = self.reconnection_count
        
        logger.info(f"Handled reconnection for call {self.call_id}, count: {self.reconnection_count}")
        return await self.save_websocket_data()
    
    def get_pending_acks(self) -> Set[str]:
        """Get set of pending acknowledgment message IDs"""
        return self.pending_acks.copy()
    
    def get_message_correlation(self, message_id: str) -> Optional[str]:
        """Get internal ID for message ID"""
        return self.message_correlation.get(message_id)
    
    def get_websocket_stats(self) -> Dict[str, Any]:
        """Get WebSocket connection statistics"""
        return {
            "connection_id": self.connection_id,
            "reconnection_count": self.reconnection_count,
            "pending_acks_count": len(self.pending_acks),
            "message_count": self.websocket_metadata.get("message_count", 0),
            "last_activity": self.websocket_metadata.get("last_activity"),
            "connection_established": self.websocket_metadata.get("connection_established"),
            "protocol_version": self.websocket_metadata.get("protocol_version")
        }

    async def mark_as_failed(self, error_message: str) -> bool:
        """
        Mark the call context as failed due to a fatal error

        Args:
            error_message: Description of the failure

        Returns:
            True if marked successfully
        """
        try:
            # Update state to indicate failure
            await self.set_state("failed")

            # Add failure information to metadata
            self.websocket_metadata["failed"] = True
            self.websocket_metadata["failure_reason"] = error_message
            self.websocket_metadata["failure_timestamp"] = get_current_iso_timestamp_fresh()

            # Clear pending acknowledgments since connection is likely dead
            self.pending_acks.clear()

            # Save the updated context
            await self.save_websocket_data()

            logger.info(f"Marked call {self.call_id} as failed: {error_message}")
            return True

        except Exception as e:
            logger.error(f"Error marking call {self.call_id} as failed: {e}")
            return False
    
    async def clear_websocket_data(self) -> bool:
        """
        Clear WebSocket-specific data from Redis
        
        Returns:
            True if cleared successfully
        """
        try:
            from shared.redis.adapters.python_adapter import get_python_adapter
            adapter = get_python_adapter()
            redis_client = adapter.connection_manager.get_async_client()
            
            ws_key = f"call:websocket:{self.call_id}"
            await redis_client.delete(ws_key)
            
            logger.debug(f"Cleared WebSocket data for call {self.call_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing WebSocket data for {self.call_id}: {e}")
            return False
    
    async def clear(self) -> bool:
        """
        Clear both base context and WebSocket data
        
        Returns:
            True if both clears successful
        """
        base_cleared = await super().clear()
        ws_cleared = await self.clear_websocket_data()
        
        return base_cleared and ws_cleared

class WebSocketContextManager:
    """
    Manages WebSocket call contexts and their lifecycle
    """
    
    def __init__(self):
        """Initialize context manager"""
        self.active_contexts: Dict[str, WebSocketCallContext] = {}  # call_id -> context
        self.connection_contexts: Dict[str, str] = {}  # connection_id -> call_id
    
    async def get_context(self, call_id: str) -> Optional[WebSocketCallContext]:
        """Get context by call ID"""
        if call_id in self.active_contexts:
            return self.active_contexts[call_id]
        
        # Try to load from Redis
        try:
            ctx = await WebSocketCallContext.get_or_create_websocket(call_id=call_id)
            self.active_contexts[call_id] = ctx
            if ctx.connection_id:
                self.connection_contexts[ctx.connection_id] = call_id
            return ctx
        except Exception as e:
            logger.error(f"Error loading context for call {call_id}: {e}")
            return None
    
    async def get_context_by_connection(self, connection_id: str) -> Optional[WebSocketCallContext]:
        """Get context by connection ID"""
        call_id = self.connection_contexts.get(connection_id)
        if call_id:
            return await self.get_context(call_id)
        return None
    
    async def create_context(self, call_id: str, hospital_id: str, 
                           caller_number: str, connection_id: str) -> WebSocketCallContext:
        """Create new context"""
        ctx = await WebSocketCallContext.get_or_create_websocket(
            call_id=call_id,
            hospital_id=hospital_id,
            caller_number=caller_number,
            connection_id=connection_id
        )
        
        self.active_contexts[call_id] = ctx
        self.connection_contexts[connection_id] = call_id
        
        return ctx
    
    async def remove_context(self, call_id: str) -> bool:
        """Remove context from memory and Redis"""
        ctx = self.active_contexts.pop(call_id, None)
        if ctx:
            if ctx.connection_id and ctx.connection_id in self.connection_contexts:
                del self.connection_contexts[ctx.connection_id]
            await ctx.clear()
            return True
        return False
    
    def get_active_calls(self) -> List[str]:
        """Get list of active call IDs"""
        return list(self.active_contexts.keys())
    
    def get_stats(self) -> Dict[str, Any]:
        """Get context manager statistics"""
        return {
            "active_contexts": len(self.active_contexts),
            "connection_mappings": len(self.connection_contexts),
            "call_ids": list(self.active_contexts.keys())
        }

# Global context manager instance
websocket_context_manager = WebSocketContextManager()
