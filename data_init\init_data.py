#!/usr/bin/env python
"""
Data Initialization Script for VoiceHealthPortal

This script loads data from JSON files and inserts it into Firebase (Firestore) and PostgreSQL databases.
It handles all the models defined in the system including:
- Hospitals
- Doctors
- Tests
- Staff Members
- Appointments
- Test Bookings

Usage:
    python init_data.py

Environment Variables:
    DATABASE_URL: PostgreSQL connection string
    GOOGLE_APPLICATION_CREDENTIALS: Path to Firebase service account key file
"""

import os
import json
import asyncio
import logging
import argparse
import base64
from datetime import datetime
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

import firebase_admin
from firebase_admin import credentials, firestore
import bcrypt
import psycopg2
from psycopg2.extras import RealDictCursor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("data_init")

# Path to JSON data files
DATA_DIR = Path(__file__).parent / "data"

# --- Firestore Helper --- 
def delete_collection(coll_ref, batch_size):
    """Deletes all documents in a Firestore collection or subcollection.
    Returns the total number of documents deleted in this call and subsequent recursive calls.
    """
    docs = coll_ref.limit(batch_size).stream()
    deleted_in_this_batch = 0

    for doc in docs:
        logger.info(f"Deleting doc {doc.id} from {coll_ref.id} => {doc.to_dict()}")
        doc.reference.delete()
        deleted_in_this_batch += 1

    total_deleted = deleted_in_this_batch
    if deleted_in_this_batch >= batch_size:
        # If we deleted a full batch, there might be more. Recurse.
        total_deleted += delete_collection(coll_ref, batch_size)
    
    # Log for this specific batch operation, not the grand total from this function call alone.
    # The caller (main_async) will sum up if it calls this multiple times or relies on the recursion for total.
    if deleted_in_this_batch > 0:
        logger.info(f"Completed a batch deletion from {coll_ref.id}, removed {deleted_in_this_batch} documents in this specific operation.")
    elif not any(coll_ref.limit(1).stream()): # Check if collection is now empty if nothing was deleted in this batch
        logger.info(f"Collection {coll_ref.id} appears to be empty or no more documents matched the batch limit.")

    return total_deleted

# Initialize Firebase Admin SDK
def init_firebase():
    """Initialize Firebase Admin SDK if not already initialized"""
    try:
        firebase_admin.get_app()
        logger.info("Firebase already initialized")
    except ValueError:
        # Check for credentials file
        cred_path = os.environ.get('GOOGLE_APPLICATION_CREDENTIALS')
        if not cred_path:
            logger.error("GOOGLE_APPLICATION_CREDENTIALS environment variable not set")
            logger.info("Please set the GOOGLE_APPLICATION_CREDENTIALS environment variable to point to your Firebase service account key file")
            return False
        
        try:
            cred = credentials.Certificate(cred_path)
            firebase_admin.initialize_app(cred)
            logger.info("Firebase initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Error initializing Firebase: {e}")
            return False
    
    return True

# Dictionary to store PostgreSQL connections for each hospital
postgres_connections = {}

# --- Encryption Helper Functions ---
def get_encryption_key():
    """Get or generate encryption key for API keys"""
    master_key = os.environ.get('ENCRYPTION_MASTER_KEY')
    if not master_key:
        # Generate a new master key for development
        master_key = base64.urlsafe_b64encode(os.urandom(32)).decode()
        logger.warning(f"Generated new encryption master key: {master_key}")
        logger.warning("Please set ENCRYPTION_MASTER_KEY environment variable with this key for production")

    # Derive key using PBKDF2
    salt = b'hospital_api_keys_salt_2024'  # Fixed salt for consistency
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),     
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(master_key.encode()))
    return Fernet(key)

def encrypt_api_key(api_key, hospital_id):
    """Encrypt API key with hospital context"""
    try:
        if not api_key or not hospital_id:
            return None

        fernet = get_encryption_key()

        # Create data structure with metadata
        data = {
            'api_key': api_key,
            'hospital_id': hospital_id,
            'created_at': datetime.now().isoformat(),
            'version': '1.0'
        }

        # Encrypt the JSON data
        encrypted_data = fernet.encrypt(json.dumps(data).encode())

        return {
            'encrypted': base64.b64encode(encrypted_data).decode(),
            'hospital_id': hospital_id,
            'algorithm': 'fernet',
            'created_at': datetime.now().isoformat(),
            'version': '1.0'
        }

    except Exception as e:
        logger.error(f"Error encrypting API key for hospital {hospital_id}: {e}")
        return None

def decrypt_api_key(encrypted_data):
    """Decrypt API key"""
    try:
        if not encrypted_data or not isinstance(encrypted_data, dict):
            return None

        fernet = get_encryption_key()

        # Decrypt the data
        encrypted_bytes = base64.b64decode(encrypted_data['encrypted'])
        decrypted_data = fernet.decrypt(encrypted_bytes)

        # Parse JSON
        data = json.loads(decrypted_data.decode())

        return data['api_key']

    except Exception as e:
        logger.error(f"Error decrypting API key: {e}")
        return None

# Initialize PostgreSQL connection for a specific hospital
def init_postgres_for_hospital(hospital_id, db_url=None):
    """Initialize PostgreSQL connection for a specific hospital
    
    Args:
        hospital_id (str): Hospital ID
        db_url (str, optional): Database URL. If not provided, uses the DATABASE_URL environment variable
        
    Returns:
        connection: PostgreSQL connection object or None if connection failed
    """
    # If connection already exists and is valid, return it
    if hospital_id in postgres_connections:
        try:
            conn = postgres_connections[hospital_id]
            cursor = conn.cursor()
            cursor.execute('SELECT 1')
            cursor.close()
            logger.info(f"Using existing PostgreSQL connection for hospital {hospital_id}")
            return conn
        except Exception:
            # Connection is dead, close it and create a new one
            try:
                postgres_connections[hospital_id].close()
            except Exception:
                pass
            del postgres_connections[hospital_id]
    
    # If no specific db_url provided, use the environment variable
    if not db_url:
        db_url = os.environ.get('DATABASE_URL')
        if not db_url:
            logger.error("DATABASE_URL environment variable not set and no db_url provided")
            logger.info("Please set the DATABASE_URL environment variable or provide a db_url")
            return None
    
    try:
        conn = psycopg2.connect(db_url)
        logger.info(f"PostgreSQL connection established for hospital {hospital_id}")
        postgres_connections[hospital_id] = conn
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL for hospital {hospital_id}: {e}")
        return None

# Close all PostgreSQL connections
def close_all_postgres_connections():
    """Close all PostgreSQL connections"""
    for hospital_id, conn in postgres_connections.items():
        try:
            conn.close()
            logger.info(f"Closed PostgreSQL connection for hospital {hospital_id}")
        except Exception as e:
            logger.error(f"Error closing PostgreSQL connection for hospital {hospital_id}: {e}")

# Load JSON data from file
def load_json_data(filename):
    """Load data from a JSON file"""
    file_path = DATA_DIR / filename
    try:
        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return None
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"Loaded data from {filename}")
        return data
    except Exception as e:
        logger.error(f"Error loading data from {filename}: {e}")
        return None

# Insert hospital data into Firestore
def insert_hospital_data(db, hospital_entry_from_json):
    """Insert a single hospital's data into Firestore using the 'hospital_ID_data' convention."""
    try:
        hospital_id = hospital_entry_from_json.get('id')
        if not hospital_id:
            logger.error("Hospital entry from JSON is missing 'id'")
            return False

        # Create the document ID as hospital_ID_data
        doc_id = f"hospital_{hospital_id}_data"
        hospital_ref = db.collection('hospitals').document(doc_id)

        # Prepare data: copy all fields, then rename db_postgres
        data_to_store = hospital_entry_from_json.copy()
        if 'db_postgres' in data_to_store:
            data_to_store['db_connection_string'] = data_to_store.pop('db_postgres')
        else:
            logger.warning(f"'db_postgres' field not found for hospital {hospital_id}. 'db_connection_string' will be missing.")

        # Extract jambonz_application_sid from sip_trunk and add it to the main dict
        if 'sip_trunk' in data_to_store and 'jambonz_application_sid' in data_to_store['sip_trunk']:
            data_to_store['jambonz_application_sid'] = data_to_store['sip_trunk'].pop('jambonz_application_sid')

        # Encrypt WhatsApp API key if present
        if 'whatsapp' in data_to_store and 'api_key' in data_to_store['whatsapp']:
            api_key = data_to_store['whatsapp']['api_key']
            if api_key and api_key != 'your_api_key_here':  # Skip placeholder values
                encrypted_api_key = encrypt_api_key(api_key, hospital_id)
                if encrypted_api_key:
                    data_to_store['whatsapp']['encrypted_api_key'] = encrypted_api_key
                    # Remove the plain text API key
                    del data_to_store['whatsapp']['api_key']
                    logger.info(f"Encrypted WhatsApp API key for hospital {hospital_id}")
                else:
                    logger.error(f"Failed to encrypt WhatsApp API key for hospital {hospital_id}")
                    # Remove the plain text API key for security
                    del data_to_store['whatsapp']['api_key']
            else:
                # Remove placeholder or empty API key
                del data_to_store['whatsapp']['api_key']
                logger.warning(f"Removed placeholder/empty WhatsApp API key for hospital {hospital_id}")

        # Remove 'id' from the data to be stored if it's not needed as a field in the doc
        # (since it's part of the doc ID). Keep it if your app logic expects it.
        # data_to_store.pop('id', None)

        hospital_ref.set(data_to_store)
        logger.info(f"Inserted/Updated hospital data for {doc_id} with content: {data_to_store}")
        return True
    except Exception as e:
        logger.error(f"Error inserting hospital data for ID {hospital_id}: {e}")
        return False

# Insert hospital data into PostgreSQL
def insert_hospital_postgres(hospital_data):
    """Insert hospital data into PostgreSQL for staff portal
    
    This function connects to the hospital-specific database and creates/updates the hospital record
    
    Args:
        hospital_data (dict): Hospital data dictionary
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        hospital_id = hospital_data.get('id')
        if not hospital_id:
            logger.error("Hospital ID is required")
            return False
        
        # Get the hospital-specific database URL
        db_url = hospital_data.get('db_connection_string')
        if not db_url:
            logger.error(f"No database URL found for hospital {hospital_id}")
            return False
        
        # Connect to the hospital-specific database
        conn = init_postgres_for_hospital(hospital_id, db_url)
        if not conn:
            logger.error(f"Failed to connect to database for hospital {hospital_id}")
            return False
        
        cursor = conn.cursor()
        
        # Check if hospital already exists
        cursor.execute("SELECT id FROM hospitals WHERE id = %s", (hospital_id,))
        if cursor.fetchone():
            logger.info(f"Hospital {hospital_id} already exists in PostgreSQL, updating...")
            query = """
            UPDATE hospitals 
            SET name = %s, address = %s, phone = %s, email = %s, settings = %s, updated_at = NOW()
            WHERE id = %s
            """
            cursor.execute(query, (
                hospital_data.get('name', 'Unknown Hospital'),
                hospital_data.get('address', ''),
                hospital_data.get('phone', ''),
                hospital_data.get('email', ''),
                json.dumps(hospital_data.get('settings', {})),
                hospital_id
            ))
        else:
            # Insert new hospital
            query = """
            INSERT INTO hospitals (id, name, address, phone, email, settings, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
            """
            cursor.execute(query, (
                hospital_id,
                hospital_data.get('name', 'Unknown Hospital'),
                hospital_data.get('address', ''),
                hospital_data.get('phone', ''),
                hospital_data.get('email', ''),
                json.dumps(hospital_data.get('settings', {}))
            ))
        
        conn.commit()
        logger.info(f"Inserted/updated hospital {hospital_id} in PostgreSQL")
        return True
    except Exception as e:
        # If we have a connection, rollback
        if 'conn' in locals() and conn:
            conn.rollback()
        logger.error(f"Error inserting hospital data into PostgreSQL: {e}")
        return False

# Insert doctor data into Firestore
def insert_doctor_data(db, hospital_id, doctors_data):
    """Insert doctor data into Firestore"""
    try:
        # Get reference to the doctors collection
        doctors_collection = db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors')
        
        # Insert each doctor
        for doctor in doctors_data:
            doctor_id = doctor.get('id')
            if not doctor_id:
                logger.warning("Doctor ID is missing, generating a random ID")
                doctor_ref = doctors_collection.document()
            else:
                doctor_ref = doctors_collection.document(doctor_id)
            
            # Set doctor data
            doctor_ref.set({
                'name': doctor.get('name', 'Unknown Doctor'),
                'specialty': doctor.get('specialty', 'General Medicine'),
                'schedule': doctor.get('schedule', {}),
                'availability': doctor.get('availability', {}),
                'price': doctor.get('price', 0.0),
                'daily_booking_limits': doctor.get('daily_booking_limits', {}),
                'current_bookings': doctor.get('current_bookings', {}),
                'next_available_dates': doctor.get('next_available_dates', [])
            })
            
            logger.info(f"Inserted doctor {doctor.get('name')} for hospital_{hospital_id}")
        
        return True
    except Exception as e:
        logger.error(f"Error inserting doctor data: {e}")
        return False

# Insert test data into Firestore
def insert_test_data(db, hospital_id, tests_data):
    """Insert test data into Firestore"""
    try:
        # Get reference to the tests collection
        tests_collection = db.collection(f'hospital_{hospital_id}_data').document('test_info').collection('tests')
        
        # Insert each test
        for test in tests_data:
            test_id = test.get('id')
            if not test_id:
                logger.warning("Test ID is missing, generating a random ID")
                test_ref = tests_collection.document()
            else:
                test_ref = tests_collection.document(test_id)
            
            # Set test data
            test_ref.set({
                'name': test.get('name', 'Unknown Test'),
                'description': test.get('description', ''),
                'duration': test.get('duration', 30),
                'cost': test.get('cost', 0.0),
                'requirements': test.get('requirements', '')
            })
            
            logger.info(f"Inserted test {test.get('name')} for hospital_{hospital_id}")
        
        return True
    except Exception as e:
        logger.error(f"Error inserting test data: {e}")
        return False

# Insert staff data into Firestore
def insert_staff_data(db, hospital_id, staff_data):
    """Insert staff data into Firestore"""
    try:
        # In the staff portal, staff data is stored directly in the 'staff' collection
        # with hospital_id as a field, not in a subcollection
        staff_collection = db.collection('staff')
        
        # Insert each staff member
        for staff in staff_data:
            staff_id = staff.get('id')
            if not staff_id:
                logger.warning("Staff ID is missing, generating a random ID")
                staff_ref = staff_collection.document()
            else:
                staff_ref = staff_collection.document(staff_id)
            
            # Get credentials data
            credentials = staff.get('credentials', {})
            
            # Hash the password properly using bcrypt
            if 'password_hash' in credentials:
                # Generate a salt and hash the password
                salt = bcrypt.gensalt(10)
                password = credentials['password_hash']  # In this case, password_hash contains the plain password
                hashed_password = bcrypt.hashpw(password.encode('utf-8'), salt)
                credentials['password_hash'] = hashed_password.decode('utf-8')  # Store as string
            
            # Set staff data with proper structure for staff portal
            staff_data_to_insert = {
                'name': staff.get('name', 'Unknown Staff'),
                'email': staff.get('email', ''),
                'role': staff.get('role', 'staff'),
                'hospital_id': hospital_id,
                'credentials': credentials,
                'contact': staff.get('contact', {}),
                'settings': staff.get('settings', {}),
                'created_at': firestore.SERVER_TIMESTAMP,
                'updated_at': firestore.SERVER_TIMESTAMP
            }
            
            staff_ref.set(staff_data_to_insert)
            
            logger.info(f"Inserted staff {staff.get('name')} for hospital {hospital_id}")
        
        return True
    except Exception as e:
        logger.error(f"Error inserting staff data: {e}")
        return False

# Note: Staff data is primarily stored in Firebase for the staff portal
# IMPORTANT: Appointments are stored ONLY in PostgreSQL (not in Firebase)
# Each hospital has its own PostgreSQL database with an appointments table

# Insert appointment data into PostgreSQL
def insert_appointment_data(hospital_id, appointments_data):
    """Insert appointment data into PostgreSQL for a specific hospital
    
    Args:
        hospital_id (str): Hospital ID
        appointments_data (list): List of appointment data dictionaries
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get connection for this hospital
        conn = postgres_connections.get(hospital_id)
        if not conn:
            logger.error(f"No PostgreSQL connection found for hospital {hospital_id}")
            return False
            
        cursor = conn.cursor()
        
        # Insert each appointment
        for appointment in appointments_data:
            appointment_id = appointment.get('id')
            if not appointment_id:
                logger.warning("Appointment ID is required, skipping")
                continue
            
            # Check if appointment already exists
            cursor.execute("SELECT id FROM appointments WHERE id = %s", (appointment_id,))
            if cursor.fetchone():
                logger.info(f"Appointment {appointment_id} already exists in PostgreSQL, updating...")
                query = """
                UPDATE appointments 
                SET patient_id = %s, doctor_id = %s, hospital_id = %s, 
                    start_time = %s, end_time = %s, status = %s, notes = %s, updated_at = NOW()
                WHERE id = %s
                """
                cursor.execute(query, (
                    appointment.get('patient_id', ''),
                    appointment.get('doctor_id', ''),
                    hospital_id,
                    appointment.get('start_time', None),
                    appointment.get('end_time', None),
                    appointment.get('status', 'scheduled'),
                    appointment.get('notes', ''),
                    appointment_id
                ))
            else:
                # Insert new appointment
                query = """
                INSERT INTO appointments (id, patient_id, doctor_id, hospital_id, 
                                         start_time, end_time, status, notes, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                """
                cursor.execute(query, (
                    appointment_id,
                    appointment.get('patient_id', ''),
                    appointment.get('doctor_id', ''),
                    hospital_id,
                    appointment.get('start_time', None),
                    appointment.get('end_time', None),
                    appointment.get('status', 'scheduled'),
                    appointment.get('notes', '')
                ))
        
        conn.commit()
        logger.info(f"Inserted/updated appointments for hospital {hospital_id} in PostgreSQL")
        return True
    except Exception as e:
        # If we have a connection, rollback
        if 'conn' in locals() and conn:
            conn.rollback()
        logger.error(f"Error inserting appointment data into PostgreSQL: {e}")
        return False

# Insert test booking data into PostgreSQL
def insert_test_booking_data(hospital_id, test_bookings_data):
    """Insert test booking data into PostgreSQL for a specific hospital
    
    Args:
        hospital_id (str): Hospital ID
        test_bookings_data (list): List of test booking data dictionaries
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get connection for this hospital
        conn = postgres_connections.get(hospital_id)
        if not conn:
            logger.error(f"No PostgreSQL connection found for hospital {hospital_id}")
            return False
            
        cursor = conn.cursor()
        
        # Insert each test booking
        for booking in test_bookings_data:
            # For test bookings, we'll use the database-generated ID
            query = """
            INSERT INTO test_bookings (patient_name, phone, test_type_id, hospital_id, 
                                      booking_time, status, notes, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            """
            cursor.execute(query, (
                booking.get('patient_name', 'Unknown Patient'),
                booking.get('phone', ''),
                booking.get('test_type_id', ''),
                hospital_id,
                booking.get('booking_time', None),
                booking.get('status', 'scheduled'),
                booking.get('notes', '')
            ))
        
        conn.commit()
        logger.info(f"Inserted test bookings for hospital {hospital_id} in PostgreSQL")
        return True
    except Exception as e:
        # If we have a connection, rollback
        if 'conn' in locals() and conn:
            conn.rollback()
        logger.error(f"Error inserting test booking data into PostgreSQL: {e}")
        return False

def _process_single_staff_member(db, staff_member_dict, hospital_id_context, all_hospitals_config):
    """Helper to process a single staff member dict for Firestore and PostgreSQL."""
    staff_id = staff_member_dict.get('id')
    if not staff_id:
        logger.warning(f"Skipping staff member due to missing 'id': {staff_member_dict}")
        return

    # Determine hospital_id for Firestore path
    hospital_id_for_firestore = staff_member_dict.get('hospital_id', hospital_id_context)

    if hospital_id_for_firestore:
        staff_doc_ref = db.collection(f'hospital_{hospital_id_for_firestore}_data') \
                          .document('staff') \
                          .collection('staff') \
                          .document(staff_id)
        
        staff_member_firestore = staff_member_dict.copy()
        if 'password' in staff_member_firestore and staff_member_firestore['password']:
            try:
                hashed_password = bcrypt.hashpw(staff_member_firestore['password'].encode('utf-8'), bcrypt.gensalt())
                staff_member_firestore['password_hash'] = hashed_password.decode('utf-8')
                del staff_member_firestore['password']
            except Exception as e:
                logger.error(f"Error hashing password for staff {staff_id}: {e}")
        elif 'password' in staff_member_firestore: # Password field exists but is empty
            del staff_member_firestore['password'] # Remove empty password field
            logger.warning(f"Empty password field for staff {staff_id} removed before Firestore upsert.")

        try:
            staff_doc_ref.set(staff_member_firestore)
            logger.info(f"Upserted staff {staff_id} for hospital {hospital_id_for_firestore} into Firestore.")
        except Exception as e:
            logger.error(f"Error upserting staff {staff_id} to Firestore for hospital {hospital_id_for_firestore}: {e}")
    else:
        logger.warning(f"Skipping Firestore insert for staff {staff_id}: missing hospital_id information.")

    # PostgreSQL insert logic
    hospital_id_for_pg = staff_member_dict.get('hospital_id', hospital_id_context)
    hospital_config = all_hospitals_config.get(hospital_id_for_pg)

    if hospital_config and hospital_config.get('db_connection_string'):
        pg_conn_staff = None
        try:
            pg_conn_staff = init_postgres_for_hospital(hospital_id_for_pg, hospital_config['db_connection_string'])
            if pg_conn_staff:
                insert_staff_postgres(pg_conn_staff, staff_member_dict)
                pg_conn_staff.commit()
                logger.info(f"Inserted/Updated staff {staff_id} into PostgreSQL for hospital {hospital_id_for_pg}.")
        except Exception as e:
            logger.error(f"Error inserting staff {staff_id} to PostgreSQL for hospital {hospital_id_for_pg}: {e}")
            if pg_conn_staff:
                try:
                    pg_conn_staff.rollback()
                except Exception as rb_err:
                    logger.error(f"Error during rollback for staff {staff_id}, hospital {hospital_id_for_pg}: {rb_err}")
    elif hospital_id_for_pg:
        logger.warning(f"Skipping PostgreSQL insert for staff {staff_id}: DB connection string not found for hospital {hospital_id_for_pg}")

async def main_async(args):
    """Asynchronous main function to handle data initialization"""
    logger.info("Starting asynchronous data initialization...")

    if not init_firebase():
        logger.error("Firebase initialization failed. Exiting.")
        return

    db = firestore.client()

    # --- 1. Hospitals --- 
    logger.info("--- Processing Hospitals (Firestore) ---")
    hospitals_data = load_json_data('hospitals.json')
    if hospitals_data:
        # Delete existing documents in 'hospitals' collection first
        hospitals_coll_ref = db.collection('hospitals')
        logger.info(f"Attempting to delete all documents from '{hospitals_coll_ref.id}' collection in Firestore...")
        total_deleted_hospitals = delete_collection(hospitals_coll_ref, 100) # Process in batches of 100
        logger.info(f"Finished deleting existing hospital documents. Total deleted: {total_deleted_hospitals}.")

        # Insert new hospital data
        successful_hospitals = 0
        for hospital_entry in hospitals_data:
            if insert_hospital_data(db, hospital_entry):
                successful_hospitals += 1
        logger.info(f"Successfully inserted/updated {successful_hospitals}/{len(hospitals_data)} hospitals into Firestore.")
    else:
        logger.warning("No hospital data loaded. Skipping Firestore hospital insertion.")

    # --- 2. Doctors (Firestore and PostgreSQL) --- 
    logger.info("--- Processing Doctors ---")
    doctors_data = load_json_data('doctors.json')
    if doctors_data:
        for hospital_id, doctors in doctors_data.items():
            insert_doctor_data(db, hospital_id, doctors)
    else:
        logger.warning("No doctor data found. Skipping doctor initialization.")

    # --- 3. Staff (Firestore and PostgreSQL) --- 
    logger.info("--- Processing Staff ---")
    staff_data = load_json_data('staff.json')
    if staff_data:
        all_hospitals_config = {h['id']: h for h in hospitals_data} if hospitals_data else {}
        
        if isinstance(staff_data, dict):
            logger.info("Processing staff data as a dictionary (hospital_id -> list_of_staff).")
            for hospital_id_key, staff_list_for_hospital in staff_data.items():
                logger.info(f"Processing staff for hospital ID (from JSON key): {hospital_id_key}")
                if isinstance(staff_list_for_hospital, list):
                    for staff_member_item in staff_list_for_hospital:
                        if isinstance(staff_member_item, dict):
                            _process_single_staff_member(db, staff_member_item, hospital_id_key, all_hospitals_config)
                        else:
                            logger.warning(f"Skipping non-dict staff item under hospital {hospital_id_key}: {staff_member_item}")
                else:
                    logger.warning(f"Expected a list of staff for hospital {hospital_id_key}, but got {type(staff_list_for_hospital)}. Skipping.")
        elif isinstance(staff_data, list):
            logger.info("Processing staff data as a flat list (each staff_member has 'hospital_id').")
            for staff_member_item in staff_data:
                if isinstance(staff_member_item, dict):
                    _process_single_staff_member(db, staff_member_item, None, all_hospitals_config) # No default hospital_id from structure
                else:
                    logger.warning(f"Skipping non-dict staff item in flat list: {staff_member_item}")
        else:
            logger.warning(f"Staff data from staff.json is not a dictionary or list (type: {type(staff_data)}). Skipping staff processing.")
    else:
        logger.warning("No staff data found. Skipping staff initialization.")

    # --- 4. Appointments (PostgreSQL only, as per current script logic) ---
    # Appointments in Firestore would likely be under hospital_XXX_data/appointments/appointments/{appt_id}
    # The current script seems to target PG directly for appointments.
    logger.info("--- Processing Appointments (PostgreSQL) ---")
    appointments_data = load_json_data('appointments.json')
    if appointments_data:
        for hospital_id, appointments in appointments_data.items():
            # Make sure we have a connection for this hospital
            if hospital_id not in postgres_connections:
                logger.warning(f"No PostgreSQL connection for hospital {hospital_id}, skipping appointments")
                continue
            
            insert_appointment_data(hospital_id, appointments)
    else:
        logger.warning("No appointment data found. Skipping appointment initialization.")

    # --- 5. Test Info/Types (PostgreSQL for available_tests, Firestore for test_info under hospital) ---
    logger.info("--- Processing Test Info/Types ---")
    test_info_data = load_json_data('test_info.json')
    if test_info_data:
        all_hospitals_config = {h['id']: h for h in hospitals_data} if hospitals_data else {}
        for test_entry in test_info_data:
            hospital_id_for_test = test_entry.get('hospital_id')
            if hospital_id_for_test:
                # Store under hospital_XXX_data/test_info (as a document)
                test_info_doc_ref = db.collection(f'hospital_{hospital_id_for_test}_data').document('test_info')
                # If test_info.json contains a list of tests for THAT hospital, structure it accordingly
                # Assuming test_entry is the content for the 'test_info' document for that hospital
                data_for_test_info_doc = test_entry.copy() # Or structure as needed
                test_info_doc_ref.set(data_for_test_info_doc) 
                logger.info(f"Upserted test_info document for hospital {hospital_id_for_test} into Firestore.")

                # If 'available_tests' are part of this and go to PG:
                hospital_config = all_hospitals_config.get(hospital_id_for_test)
                if hospital_config and hospital_config.get('db_connection_string') and 'available_tests' in test_entry:
                    pg_conn_test = init_postgres_for_hospital(hospital_id_for_test, hospital_config['db_connection_string'])
                    if pg_conn_test:
                        for available_test_item in test_entry['available_tests']:
                            insert_available_test_postgres(pg_conn_test, available_test_item, hospital_id_for_test)
                        pg_conn_test.commit()
            else:
                logger.warning(f"Skipping Firestore insert for test_info: missing hospital_id in entry {test_entry.get('test_name')}")
    else:
        logger.warning("No test_info data found. Skipping test_info initialization.")

    # --- 6. Test Bookings (PostgreSQL only, as per current script logic) ---
    # Test bookings in Firestore would likely be under hospital_XXX_data/test_info/test_bookings/{booking_id}
    # or hospital_XXX_data/test_bookings/test_bookings/{booking_id}
    logger.info("--- Processing Test Bookings (PostgreSQL) ---")
    test_bookings_data = load_json_data('test_bookings.json')
    if test_bookings_data:
        for hospital_id, bookings in test_bookings_data.items():
            # Make sure we have a connection for this hospital
            if hospital_id not in postgres_connections:
                logger.warning(f"No PostgreSQL connection for hospital {hospital_id}, skipping test bookings")
                continue
            
            insert_test_booking_data(hospital_id, bookings)
    else:
        logger.warning("No test booking data found. Skipping test booking initialization.")

    logger.info("Data initialization completed successfully")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Data Initialization Script for VoiceHealthPortal')
    # Add any command-line arguments if needed, e.g., to specify which parts to run
    # parser.add_argument('--sync-hospitals', action='store_true', help='Sync hospitals data only')

    args = parser.parse_args()
    asyncio.run(main_async(args))
    close_all_postgres_connections()
    logger.info("Data initialization script finished.")
