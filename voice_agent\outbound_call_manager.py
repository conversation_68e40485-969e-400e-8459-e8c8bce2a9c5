import os
import asyncio
import logging
import httpx
from typing import Dict, Any, Optional

from .models import HospitalConfig
from .database import get_firestore_db
from .call_context import CallContext

logger = logging.getLogger(__name__)

class OutboundCallManager:
    def __init__(self):
        self.jambonz_account_sid = os.getenv("JAMBONZ_ACCOUNT_SID")
        self.jambonz_api_key = os.getenv("JAMBONZ_API_KEY")
        self.jambonz_rest_api_base_url = os.getenv("JAMBONZ_REST_API_BASE_URL")

        if not all([self.jambonz_account_sid, self.jambonz_api_key, self.jambonz_rest_api_base_url]):
            raise ValueError("Missing Jambonz configuration in environment variables.")

    async def get_hospital_config(self, hospital_id: str) -> Optional[Dict[str, Any]]:
        try:
            db = get_firestore_db()
            hospital_doc_ref = db.collection('hospitals').document(f'hospital_{hospital_id}_data')
            loop = asyncio.get_running_loop()
            hospital_doc = await loop.run_in_executor(None, hospital_doc_ref.get)
            if hospital_doc.exists:
                return hospital_doc.to_dict()
            else:
                logger.error(f"Hospital with ID {hospital_id} not found in Firestore.")
                return None
        except Exception as e:
            logger.error(f"Error fetching hospital config for hospital ID {hospital_id}: {e}")
            return None

    async def create_outbound_call(self, patient_number: str, hospital_id: str) -> Dict[str, Any]:
        hospital_config = await self.get_hospital_config(hospital_id)
        if not hospital_config:
            return {"success": False, "error": "Could not retrieve hospital configuration."}

        hospital_phone_number = hospital_config.get("phone")
        jambonz_application_sid = hospital_config.get("jambonz_application_sid")

        if not hospital_phone_number or not jambonz_application_sid:
            return {"success": False, "error": "Missing phone number or Jambonz application SID in hospital configuration."}

        url = f"{self.jambonz_rest_api_base_url}/v1/Accounts/{self.jambonz_account_sid}/Calls"
        headers = {
            "Authorization": f"Bearer {self.jambonz_api_key}",
            "Content-Type": "application/json",
        }
        payload = {
            "application_sid": jambonz_application_sid,
            "from": hospital_phone_number,
            "to": {
                "type": "phone",
                "number": patient_number
            },
            "call_hook": {
                "url": f"wss://{os.getenv('WEBSOCKET_HOST')}:{os.getenv('WEBSOCKET_PORT')}/ws/jambonz/{hospital_id}",
                "method": "websocket"
            },
            "call_status_hook": {
                "url": f"wss://{os.getenv('WEBSOCKET_HOST')}:{os.getenv('WEBSOCKET_PORT')}/ws/jambonz/{hospital_id}",
                "method": "websocket"
            }
        }

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(url, json=payload, headers=headers)
                response.raise_for_status()
                call_data = response.json()
                call_sid = call_data.get("sid")

                # Create a call context for the outbound call
                if call_sid:
                    ctx = await CallContext.get_or_create(
                        call_id=call_sid,
                        hospital_id=hospital_id,
                        caller_number=patient_number
                    )
                    ctx.jambonz_application_sid = jambonz_application_sid
                    await ctx.save()

                return {"success": True, "data": call_data}
            except httpx.HTTPStatusError as e:
                logger.error(f"Error creating outbound call: {e.response.text}")
                return {"success": False, "error": e.response.text}
            except Exception as e:
                logger.error(f"Error creating outbound call: {e}")
                return {"success": False, "error": str(e)}

outbound_call_manager = OutboundCallManager()
