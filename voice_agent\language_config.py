"""
Production-level language configuration for Indian voice agent system.
Hindi is the primary language with support for Bengali and English.
Designed for easy scaling to additional Indian regional languages.
"""

import os
import logging
from typing import Dict, List, Optional, Any
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)

class SupportedLanguages(Enum):
    """
    Enum for supported languages in order of priority.
    Hindi is the primary language for Indian hospitals.
    """
    HINDI = "hi"
    BENGALI = "bn" 
    ENGLISH = "en"
    
    # TODO: Add these languages when scaling:
    # TAMIL = "ta"
    # TELUGU = "te"
    # MARATHI = "mr"
    # GUJARATI = "gu"
    # KANNADA = "kn"
    # PUNJABI = "pa"
    # MALAYALAM = "ml"
    # ODIA = "or"

class LanguageConfig:
    """
    Centralized language configuration for the voice agent system.
    Provides language mappings, defaults, and regional settings.
    """
    
    # Primary language for the system (Hindi for Indian hospitals)
    PRIMARY_LANGUAGE = SupportedLanguages.HINDI.value
    
    # Default language fallback order
    DEFAULT_LANGUAGE_PRIORITY = [
        SupportedLanguages.HINDI.value,
        SupportedLanguages.BENGALI.value,
        SupportedLanguages.ENGLISH.value
    ]
    
    # Language metadata with regional information
    LANGUAGE_METADATA = {
        "hi": {
            "name": "हिंदी",
            "english_name": "Hindi",
            "script": "Devanagari",
            "region": "North India",
            "speech_code": "hi-IN",
            "locale": "hi_IN",
            "rtl": False,
            "unicode_range": (0x0900, 0x097F),
            "common_greetings": ["नमस्ते", "नमस्कार", "आदाब"],
            "doctor_titles": ["डॉक्टर", "डॉ", "वैद्य", "चिकित्सक"],
            "common_words": ["का", "की", "के", "में", "से", "को", "और", "है", "हैं", "था", "थी"],
            "filler_words": ["अरे", "वो", "यह", "तो", "ना", "हां", "जी"],
            "medical_terms": {
                "doctor": "डॉक्टर",
                "hospital": "अस्पताल",
                "appointment": "अपॉइंटमेंट",
                "test": "जांच",
                "medicine": "दवा",
                "emergency": "आपातकाल"
            },
            "keywords": {
                "doctor_appointment": ["डॉक्टर", "अपॉइंटमेंट", "doctor", "appointment"],
                "test_examination": ["टेस्ट", "जांच", "test", "examination"],
                "yes_confirm": ["हां", "हाँ", "ठीक है", "yes", "confirm", "ok"]
            }
        },
        "bn": {
            "name": "বাংলা",
            "english_name": "Bengali",
            "script": "Bengali",
            "region": "West Bengal, Bangladesh",
            "speech_code": "bn-IN",
            "locale": "bn_IN",
            "rtl": False,
            "unicode_range": (0x0980, 0x09FF),
            "common_greetings": ["নমস্কার", "আদাব", "সালাম"],
            "doctor_titles": ["ডাক্তার", "ডাঃ", "চিকিৎসক"],
            "common_words": ["এর", "এই", "সেই", "যে", "কি", "কে", "তে", "থেকে", "এবং"],
            "filler_words": ["আরে", "ওটা", "এটা", "তো", "না", "হ্যাঁ", "জি"],
            "medical_terms": {
                "doctor": "ডাক্তার",
                "hospital": "হাসপাতাল",
                "appointment": "অ্যাপয়েন্টমেন্ট",
                "test": "পরীক্ষা",
                "medicine": "ওষুধ",
                "emergency": "জরুরি"
            },
            "keywords": {
                "doctor_appointment": ["ডাক্তার", "অ্যাপয়েন্টমেন্ট", "doctor", "appointment"],
                "test_examination": ["পরীক্ষা", "টেস্ট", "test", "examination"],
                "yes_confirm": ["হ্যাঁ", "ঠিক আছে", "yes", "confirm", "ok"]
            }
        },
        "en": {
            "name": "English",
            "english_name": "English", 
            "script": "Latin",
            "region": "International",
            "speech_code": "en-IN",  # Indian English for better accent recognition
            "locale": "en_IN",
            "rtl": False,
            "unicode_range": (0x0020, 0x007F),
            "common_greetings": ["hello", "hi", "good morning", "good evening"],
            "doctor_titles": ["doctor", "dr", "doc", "physician"],
            "common_words": ["the", "a", "an", "of", "in", "to", "for", "and", "is", "are", "was", "were"],
            "filler_words": ["um", "uh", "like", "you know", "actually", "basically"],
            "medical_terms": {
                "doctor": "doctor",
                "hospital": "hospital",
                "appointment": "appointment",
                "test": "test",
                "medicine": "medicine",
                "emergency": "emergency"
            },
            "keywords": {
                "doctor_appointment": ["doctor", "appointment"],
                "test_examination": ["test", "examination"],
                "yes_confirm": ["yes", "confirm", "ok"]
            }
        }
        
        # TODO: Add configurations for additional languages when scaling:
        # "ta": {  # Tamil
        #     "name": "தமிழ்",
        #     "english_name": "Tamil",
        #     "script": "Tamil",
        #     "region": "Tamil Nadu",
        #     "speech_code": "ta-IN",
        #     "locale": "ta_IN",
        #     "unicode_range": (0x0B80, 0x0BFF),
        #     ...
        # },
        # "te": {  # Telugu
        #     "name": "తెలుగు", 
        #     "english_name": "Telugu",
        #     "script": "Telugu",
        #     "region": "Andhra Pradesh, Telangana",
        #     "speech_code": "te-IN",
        #     "locale": "te_IN",
        #     "unicode_range": (0x0C00, 0x0C7F),
        #     ...
        # }
    }
    
    # Language selection prompts in each language
    LANGUAGE_SELECTION_PROMPTS = {
        "hi": "भाषा चुनने के लिए: हिंदी के लिए 1 दबाएं, बंगाली के लिए 2 दबाएं, अंग्रेजी के लिए 3 दबाएं",
        "bn": "ভাষা নির্বাচনের জন্য: হিন্দির জন্য 1 চাপুন, বাংলার জন্য 2 চাপুন, ইংরেজির জন্য 3 চাপুন",
        "en": "To select language: Press 1 for Hindi, Press 2 for Bengali, Press 3 for English"
    }
    
    # Welcome messages in each language (first greeting - introducing Megha)
    WELCOME_MESSAGES = {
        "hi": "{hospital_name} में आपका स्वागत है, मैं मेघा आपकी मदद के लिए मौजूद हूँ। इमरजेंसी के लिए 0 दबाएँ, भाषा बदलने के लिए 1 दबाएँ।",
        "bn": "{hospital_name} এ আপনাকে স্বাগতম, আমি মেঘা আপনার সাহায্যের জন্য এখানে আছি। জরুরির জন্য 0 চাপুন, ভাষা পরিবর্তনের জন্য 1 চাপুন।",
        "en": "Welcome to {hospital_name}, I am Megha here to help you. Press 0 for emergency, press 1 to change language."
    }
    
    # Error messages in each language
    ERROR_MESSAGES = {
        "hi": {
            "not_understood": "मাফ करें, मैं समझ नहीं पाया। कृपया दोबारा कहें।",
            "technical_error": "तकनीकी समस्या हो रही है। कृपया बाद में कॉल करें।",
            "invalid_selection": "गलत विकल्प। कृपया सही विकल्प चुनें।",
            "booking_limit_reached": "खुशी है, डॉक्टर {doctor_name} की आज की बुकिंग सीमा पूरी हो गई है।",
            "next_available_date": "अगली उपलब्ध तारीख {date} है।",
            "choose_different_option": "कृपया कोई अन्य डॉक्टर चुनें या {date} को अपॉइंटमेंट बुक करें।",
            "booking_limit_error": "बुकिंग सीमा जांचने में समस्या हुई। कृपया बाद में कॉल करें।",
            "schedule_not_available": "डॉक्टर का शेड्यूल उपलब्ध नहीं है। कृपया अस्पताल से संपर्क करें।"
        },
        "bn": {
            "not_understood": "দুঃখিত, আমি বুঝতে পারিনি। অনুগ্রহ করে আবার বলুন।",
            "technical_error": "প্রযুক্তিগত সমস্যা হচ্ছে। অনুগ্রহ করে পরে কল করুন।",
            "invalid_selection": "ভুল নির্বাচন। অনুগ্রহ করে সঠিক বিকল্প বেছে নিন।",
            "booking_limit_reached": "দুঃখিত, ডাক্তার {doctor_name} এর আজকের বুকিং সীমা পূর্ণ হয়ে গেছে।",
            "next_available_date": "পরবর্তী উপলব্ধ তারিখ {date}।",
            "choose_different_option": "অনুগ্রহ করে অন্য ডাক্তার বেছে নিন বা {date} তারিখে অ্যাপয়েন্টমেন্ট বুক করুন।",
            "booking_limit_error": "বুকিং সীমা পরীক্ষা করতে সমস্যা হয়েছে। অনুগ্রহ করে পরে কল করুন।",
            "schedule_not_available": "ডাক্তারের সময়সূচী উপলব্ধ নেই। অনুগ্রহ করে হাসপাতালের সাথে যোগাযোগ করুন।"
        },
        "en": {
            "not_understood": "Sorry, I didn't understand. Please try again.",
            "technical_error": "We're experiencing technical difficulties. Please call back later.",
            "invalid_selection": "Invalid selection. Please choose a valid option.",
            "booking_limit_reached": "Sorry, Dr. {doctor_name}'s booking limit for today is full.",
            "next_available_date": "The next available date is {date}.",
            "choose_different_option": "Please choose a different doctor or book an appointment on {date}.",
            "booking_limit_error": "Error checking booking limit. Please call back later.",
            "schedule_not_available": "Doctor's schedule is not available. Please contact the hospital."
        }
    }

    @classmethod
    def get_primary_language(cls) -> str:
        """Get the primary language code."""
        return cls.PRIMARY_LANGUAGE

    @classmethod
    def get_supported_languages(cls) -> List[str]:
        """Get list of supported language codes in priority order."""
        return cls.DEFAULT_LANGUAGE_PRIORITY.copy()

    @classmethod
    def get_safe_language_fallback(cls, language_code: str) -> str:
        """
        Get a safe language fallback that is guaranteed to exist in LANGUAGE_METADATA.

        Fallback order:
        1. Requested language (if valid and exists in metadata)
        2. Primary language (Hindi)
        3. First available language from priority list
        4. Ultimate fallback ('hi' - hardcoded)

        Args:
            language_code: The requested language code

        Returns:
            A language code that is guaranteed to exist in LANGUAGE_METADATA
        """
        # Step 1: Check if requested language exists
        if language_code and language_code in cls.LANGUAGE_METADATA:
            return language_code

        # Step 2: Try primary language
        if cls.PRIMARY_LANGUAGE in cls.LANGUAGE_METADATA:
            return cls.PRIMARY_LANGUAGE

        # Step 3: Try first available language from priority list
        for lang in cls.DEFAULT_LANGUAGE_PRIORITY:
            if lang in cls.LANGUAGE_METADATA:
                return lang

        # Step 4: Try any available language from metadata
        if cls.LANGUAGE_METADATA:
            return next(iter(cls.LANGUAGE_METADATA.keys()))

        # Step 5: Ultimate fallback (should never reach here in production)
        logger.error("Critical: No languages found in LANGUAGE_METADATA. Using hardcoded fallback.")
        return "hi"  # Hardcoded ultimate fallback

    @classmethod
    def get_language_metadata(cls, language_code: str) -> Dict[str, Any]:
        """Get metadata for a specific language with safe fallback."""
        safe_fallback = cls.get_safe_language_fallback(language_code)
        return cls.LANGUAGE_METADATA.get(language_code, cls.LANGUAGE_METADATA.get(safe_fallback, {}))
    
    @classmethod
    def get_speech_code(cls, language_code: str) -> str:
        """Get speech recognition code for a language."""
        metadata = cls.get_language_metadata(language_code)
        return metadata.get("speech_code", "hi-IN")
    
    @classmethod
    def get_language_name(cls, language_code: str, in_language: str = None) -> str:
        """Get language name, optionally in a specific language."""
        metadata = cls.get_language_metadata(language_code)
        if in_language == "en":
            return metadata.get("english_name", "Hindi")
        return metadata.get("name", "हिंदी")
    
    @classmethod
    def detect_language_from_text(cls, text: str) -> str:
        """
        Detect language from text using Unicode ranges with improved handling for mixed-language text.
        Returns the most likely language code.
        """
        if not text:
            return cls.PRIMARY_LANGUAGE

        # Remove non-alphabetic characters for better detection
        cleaned_text = ''.join(c for c in text if c.isalpha())
        if not cleaned_text:
            return cls.PRIMARY_LANGUAGE

        # Count characters in each script
        script_counts = {}

        for char in cleaned_text:
            char_code = ord(char)
            for lang_code, metadata in cls.LANGUAGE_METADATA.items():
                unicode_range = metadata.get("unicode_range")
                if unicode_range and unicode_range[0] <= char_code <= unicode_range[1]:
                    script_counts[lang_code] = script_counts.get(lang_code, 0) + 1

        # Only return a language if it has significant representation
        total_chars = len(cleaned_text)
        if script_counts:
            max_lang = max(script_counts, key=script_counts.get)
            # Require at least 30% of characters to be from the detected script
            if script_counts[max_lang] / total_chars >= 0.3:
                return max_lang

        # Default to primary language if no script detected or insufficient confidence
        return cls.PRIMARY_LANGUAGE
    
    @classmethod
    def get_default_language_for_hospital(cls, hospital_id: str) -> str:
        """
        Get default language for a hospital.
        Can be customized per hospital in the future.
        """
        # TODO: Implement hospital-specific language preferences
        # For now, return primary language (Hindi)
        return cls.PRIMARY_LANGUAGE
    
    @classmethod
    def get_language_selection_prompt(cls, language: str = None) -> str:
        """Get language selection prompt in specified language with safe fallback."""
        if not language:
            language = cls.PRIMARY_LANGUAGE

        safe_language = cls.get_safe_language_fallback(language)
        fallback_language = cls.get_safe_language_fallback(cls.PRIMARY_LANGUAGE)

        return cls.LANGUAGE_SELECTION_PROMPTS.get(
            language,
            cls.LANGUAGE_SELECTION_PROMPTS.get(
                safe_language,
                cls.LANGUAGE_SELECTION_PROMPTS.get(
                    fallback_language,
                    "भाषा चुनने के लिए: हिंदी के लिए 1 दबाएं, बंगाली के लिए 2 दबाएं, अंग्रेजी के लिए 3 दबाएं"  # Hardcoded Hindi fallback
                )
            )
        )

    @classmethod
    def get_welcome_message(cls, language: str, hospital_name: str = "") -> str:
        """Get welcome message in specified language with safe fallback."""
        safe_language = cls.get_safe_language_fallback(language)
        fallback_language = cls.get_safe_language_fallback(cls.PRIMARY_LANGUAGE)

        template = cls.WELCOME_MESSAGES.get(
            language,
            cls.WELCOME_MESSAGES.get(
                safe_language,
                cls.WELCOME_MESSAGES.get(
                    fallback_language,
                    "{hospital_name} में आपका स्वागत है, मैं मेघा आपकी मदद के लिए मौजूद हूँ। इमरजेंसी के लिए 0 दबाएँ, भाषा बदलने के लिए 1 दबाएँ।"  # Updated Hindi fallback
                )
            )
        )

        # Use hospital name or default
        if not hospital_name:
            hospital_name = "अस्पताल" if language == "hi" else "হাসপাতাল" if language == "bn" else "Hospital"

        return template.format(hospital_name=hospital_name)

    @classmethod
    def get_language_selection_message(cls, language: str, language_options: str = "") -> str:
        """Get language selection message in specified language with safe fallback."""
        safe_language = cls.get_safe_language_fallback(language)
        fallback_language = cls.get_safe_language_fallback(cls.PRIMARY_LANGUAGE)

        template = cls.LANGUAGE_SELECTION_PROMPTS.get(
            language,
            cls.LANGUAGE_SELECTION_PROMPTS.get(
                safe_language,
                cls.LANGUAGE_SELECTION_PROMPTS.get(
                    fallback_language,
                    "भाषा चुनने के लिए: हिंदी के लिए 1 दबाएं, बंगाली के लिए 2 दबाएं, अंग्रेजी के लिए 3 दबाएं"  # Hindi fallback
                )
            )
        )

        # If custom language options provided, use them, otherwise use the template
        return language_options if language_options else template

    @classmethod
    def get_error_message(cls, error_type: str, language: str, **kwargs) -> str:
        """Get error message in specified language with safe fallback and optional formatting."""
        safe_language = cls.get_safe_language_fallback(language)
        fallback_language = cls.get_safe_language_fallback(cls.PRIMARY_LANGUAGE)

        # Get error messages with safe fallback
        error_messages = cls.ERROR_MESSAGES.get(
            language,
            cls.ERROR_MESSAGES.get(
                safe_language,
                cls.ERROR_MESSAGES.get(
                    fallback_language,
                    {  # Hardcoded Hindi fallback messages
                        "not_understood": "माफ करें, मैं समझ नहीं पाया। कृपया दोबारा कहें।",
                        "technical_error": "तकनीकी समस्या हो रही है। कृपया बाद में कॉल करें।",
                        "invalid_selection": "गलत विकल्प। कृपया सही विकल्प चुनें।"
                    }
                )
            )
        )

        # Get specific error message with fallback to "not_understood"
        message_template = error_messages.get(error_type, error_messages.get("not_understood", "माफ करें, मैं समझ नहीं पाया। कृपया दोबारा कहें।"))

        # Format the message with any provided kwargs (e.g., doctor_name, date)
        try:
            return message_template.format(**kwargs)
        except (KeyError, ValueError):
            # If formatting fails, return the template as-is
            return message_template
    
    @classmethod
    def is_supported_language(cls, language_code: str) -> bool:
        """Check if a language is supported."""
        return language_code in cls.LANGUAGE_METADATA
    
    @classmethod
    def get_medical_term(cls, term: str, language: str) -> str:
        """Get medical term translation in specified language."""
        metadata = cls.get_language_metadata(language)
        medical_terms = metadata.get("medical_terms", {})
        return medical_terms.get(term, term)

    @classmethod
    def get_default_hospital_name(cls, language: str) -> str:
        """Get default hospital name in specified language with safe fallback."""
        safe_language = cls.get_safe_language_fallback(language)

        # Language-specific default hospital names
        default_names = {
            "hi": "अस्पताल",
            "bn": "হাসপাতাল",
            "en": "Hospital"
        }

        return default_names.get(safe_language, default_names.get("hi", "Hospital"))

    @classmethod
    def get_keywords(cls, keyword_type: str, language: str = None) -> List[str]:
        """Get keywords for a specific type in specified language with fallback to all languages."""
        if language:
            metadata = cls.get_language_metadata(language)
            keywords = metadata.get("keywords", {})
            return keywords.get(keyword_type, [])
        else:
            # Return keywords from all languages for the given type
            all_keywords = []
            for lang_metadata in cls.LANGUAGE_METADATA.values():
                keywords = lang_metadata.get("keywords", {})
                all_keywords.extend(keywords.get(keyword_type, []))
            return list(set(all_keywords))  # Remove duplicates

    @classmethod
    def get_language_keywords(cls) -> Dict[str, str]:
        """
        Get centralized language keywords mapping for speech recognition.
        This prevents duplicate keys and ensures consistency across the application.

        Returns:
            Dictionary mapping language keywords to language codes
        """
        return {
            # Hindi variations
            "hindi": "hi", "हिंदी": "hi", "हिन्दी": "hi",
            # Bengali variations
            "bengali": "bn", "বাংলা": "bn", "bangla": "bn",
            # English variations
            "english": "en", "angrezi": "en", "अंग्रेजी": "en",
            # Tamil variations
            "tamil": "ta", "தமிழ்": "ta",
            # Telugu variations
            "telugu": "te", "తెలుగు": "te",
            # Gujarati variations
            "gujarati": "gu", "ગુજરાતી": "gu",
            # Kannada variations
            "kannada": "kn", "ಕನ್ನಡ": "kn",
            # Malayalam variations
            "malayalam": "ml", "മലയാളം": "ml",
            # Marathi variations
            "marathi": "mr", "मराठी": "mr",
            # Punjabi variations
            "punjabi": "pa", "ਪੰਜਾਬੀ": "pa",
            # Oriya/Odia variations
            "oriya": "or", "ଓଡ଼ିଆ": "or", "odia": "or",
            # Assamese variations
            "assamese": "as", "অসমীয়া": "as"
        }

# Global language configuration instance
language_config = LanguageConfig()

# Convenience functions for easy access
def get_primary_language() -> str:
    """Get the primary language code (Hindi)."""
    return language_config.get_primary_language()

def get_supported_languages() -> List[str]:
    """Get list of supported language codes."""
    return language_config.get_supported_languages()

def get_safe_language_fallback(language_code: str) -> str:
    """Get a safe language fallback that is guaranteed to exist."""
    return language_config.get_safe_language_fallback(language_code)

def detect_language(text: str) -> str:
    """Detect language from text."""
    return language_config.detect_language_from_text(text)

def get_speech_code(language: str) -> str:
    """Get speech recognition code for language."""
    return language_config.get_speech_code(language)

def get_language_name(language_code: str, in_language: str = None) -> str:
    """Get language name."""
    return language_config.get_language_name(language_code, in_language)
