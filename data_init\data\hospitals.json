[{"id": "123", "name": "General Hospital", "address": "123 Main Street, City, State, 12345", "phone": "+***********", "email": "<EMAIL>", "sip_trunk": {"provider": "jambonz", "sip_endpoint": "sip.jambonz.org", "auth_token": "sample_token_123", "jambonz_application_sid": "app_sid_123"}, "whatsapp": {"provider": "ycloud", "phone_number": "+***********", "webhook_path": "/whatsapp/webhook", "api_key": "ycloudapikey_hospital123_sample", "business_account_id": "waba_123456789", "webhook_secret": "webhook_secret_hospital123"}, "languages": ["en", "hi", "bn"], "db_postgres": "postgresql://hospital_user:hospitaldb@localhost:5432/hospital123", "created_at": "2025-01-01T00:00:00Z", "emergency_number": "911", "settings": {"timezone": "America/New_York", "working_hours": {"monday": "08:00-20:00", "tuesday": "08:00-20:00", "wednesday": "08:00-20:00", "thursday": "08:00-20:00", "friday": "08:00-20:00", "saturday": "09:00-17:00", "sunday": "09:00-17:00"}, "appointment_duration_minutes": 30, "logo_url": "https://example.com/hospital_logo.png", "services": ["voice", "whatsapp"]}, "scheduling_config": {"appointment_duration_minutes": 30, "working_hours": {"start": "08:00", "end": "20:00", "lunch_break": {"start": "12:00", "end": "13:00"}}, "time_slot_interval_minutes": 30, "max_slots_per_day": 24, "advance_booking_days": 30, "same_day_booking_cutoff_hours": 2, "test_booking": {"time_slot_interval_minutes": 30, "same_day_booking_cutoff_hours": 1, "max_slots_per_day": 32}}}, {"id": "456", "name": "City Medical Center", "address": "456 Health Avenue, Metropolis, State, 67890", "phone": "+***********", "email": "<EMAIL>", "sip_trunk": {"provider": "jambonz", "sip_endpoint": "sip.jambonz.org", "auth_token": "sample_token_456", "jambonz_application_sid": "app_sid_456"}, "whatsapp": {"provider": "ycloud", "phone_number": "+***********", "webhook_path": "/whatsapp/webhook", "api_key": "ycloudapikey_hospital456_sample", "business_account_id": "waba_987654321", "webhook_secret": "webhook_secret_hospital456"}, "languages": ["en", "es", "zh"], "db_postgres": "postgresql://hospital_user:hospitaldb@localhost:5432/hospital456", "created_at": "2025-01-01T00:00:00Z", "emergency_number": "911", "settings": {"timezone": "America/Los_Angeles", "services": ["voice", "whatsapp"], "working_hours": {"monday": "07:00-19:00", "tuesday": "07:00-19:00", "wednesday": "07:00-19:00", "thursday": "07:00-19:00", "friday": "07:00-19:00", "saturday": "08:00-16:00", "sunday": "08:00-16:00"}, "appointment_duration_minutes": 45, "logo_url": "https://example.com/citymedical_logo.png"}, "scheduling_config": {"appointment_duration_minutes": 45, "working_hours": {"start": "07:00", "end": "19:00", "lunch_break": {"start": "12:30", "end": "13:30"}}, "time_slot_interval_minutes": 45, "max_slots_per_day": 16, "advance_booking_days": 45, "same_day_booking_cutoff_hours": 3, "test_booking": {"time_slot_interval_minutes": 30, "same_day_booking_cutoff_hours": 2, "max_slots_per_day": 24}}}, {"id": "459", "name": "Community Health Clinic", "address": "789 Wellness Road, Smalltown, State, 54321", "phone": "+***********", "email": "<EMAIL>", "sip_trunk": {"provider": "jambonz", "sip_endpoint": "sip.jambonz.org", "auth_token": "sample_token_789", "jambonz_application_sid": "app_sid_789"}, "whatsapp": {"provider": "ycloud", "phone_number": "+***********", "webhook_path": "/whatsapp/webhook", "api_key": "ycloudapikey_hospital459_sample", "business_account_id": "waba_555666777", "webhook_secret": "webhook_secret_hospital459"}, "languages": ["en", "fr", "ar"], "db_postgres": "postgresql://hospital_user:hospitaldb@localhost:5432/hospital459", "ssh_tunnel": {"user": "ssh_user", "host": "ssh.communityclinic.org", "private_key_path": "/path/to/private_key.pem"}, "created_at": "2025-01-01T00:00:00Z", "emergency_number": "911", "settings": {"timezone": "America/Chicago", "services": ["voice", "whatsapp"], "working_hours": {"monday": "09:00-17:00", "tuesday": "09:00-17:00", "wednesday": "09:00-17:00", "thursday": "09:00-17:00", "friday": "09:00-17:00", "saturday": "10:00-14:00", "sunday": "closed"}, "appointment_duration_minutes": 30, "logo_url": "https://example.com/community_logo.png"}, "scheduling_config": {"appointment_duration_minutes": 30, "working_hours": {"start": "09:00", "end": "17:00", "lunch_break": {"start": "12:00", "end": "13:00"}}, "time_slot_interval_minutes": 30, "max_slots_per_day": 14, "advance_booking_days": 21, "same_day_booking_cutoff_hours": 1, "test_booking": {"time_slot_interval_minutes": 30, "same_day_booking_cutoff_hours": 1, "max_slots_per_day": 16}}}]